'use client'

import React from 'react'
import { Brain, ChevronDown, Mic } from 'lucide-react'
import { cn } from '@/lib/utils'

interface WelcomeScreenProps {
  onSendMessage: (content: string) => void
  isThinkMode: boolean
  onToggleThinkMode: () => void
  currentModel: string
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onSendMessage,
  isThinkMode,
  onToggleThinkMode,
  currentModel
}) => {
  const suggestedQuestions = [
    "What would happen to the Earth's climate if the planet's axial tilt were 0 degrees?",
    "Can you solve the \"infinity long\" problem?",
    "Can you create an awesome looking 3D fractal animation?",
    "Can you create a rocket simulation for a mission to mars using real orbital mechanics?"
  ]

  const handleQuestionClick = (question: string) => {
    onSendMessage(question)
  }

  return (
    <div className="flex flex-col items-center justify-center h-full px-6 py-12 animate-fade-in">
      {/* <PERSON><PERSON> and Title */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-6">
          <div className="w-20 h-20 bg-gradient-to-br from-white to-gray-200 rounded-full flex items-center justify-center shadow-glow">
            <div className="w-16 h-16 bg-gradient-to-br from-black to-gray-800 rounded-full flex items-center justify-center">
              <div className="w-10 h-10 bg-gradient-to-br from-white to-gray-100 rounded-full"></div>
            </div>
          </div>
        </div>
        <h1 className="text-5xl font-bold gradient-text mb-3">Kana</h1>
        <p className="text-xl text-foreground-secondary">Know more, Be more</p>
      </div>

      {/* Main Input Area */}
      <div className="w-full max-w-3xl mb-12">
        <div className="relative glass-effect rounded-2xl p-1">
          <input
            type="text"
            placeholder="What do you want to know?"
            className="w-full px-6 py-5 bg-transparent border-0 rounded-2xl text-foreground placeholder-foreground-muted focus:outline-none text-lg"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                handleQuestionClick(e.currentTarget.value)
                e.currentTarget.value = ''
              }
            }}
          />

          {/* Input Controls */}
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
            {/* Think Mode Toggle */}
            <button
              onClick={onToggleThinkMode}
              className={cn(
                "flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200",
                isThinkMode
                  ? "bg-kana-accent text-white shadow-glow"
                  : "bg-background-secondary hover:bg-background-hover text-foreground-secondary"
              )}
            >
              <Brain className="w-4 h-4" />
              <span className="text-sm font-medium">Think</span>
            </button>

            {/* Model Selector */}
            <div className="flex items-center space-x-2 px-4 py-2 bg-background-secondary hover:bg-background-hover rounded-xl transition-colors">
              <span className="text-sm text-foreground font-medium">{currentModel}</span>
              <ChevronDown className="w-4 h-4 text-foreground-secondary" />
            </div>

            {/* Voice Input */}
            <button className="p-2 hover:bg-background-hover rounded-xl transition-colors">
              <Mic className="w-5 h-5 text-foreground-secondary" />
            </button>
          </div>
        </div>
      </div>

      {/* Suggested Questions */}
      <div className="w-full max-w-3xl space-y-4">
        {suggestedQuestions.map((question, index) => (
          <button
            key={index}
            onClick={() => handleQuestionClick(question)}
            className="w-full text-left p-5 bg-background-secondary hover:bg-background-hover rounded-xl transition-all duration-200 border border-border hover:border-border-light hover:shadow-glow group"
          >
            <p className="text-foreground text-sm leading-relaxed group-hover:text-foreground transition-colors">
              {question}
            </p>
          </button>
        ))}
      </div>

      {/* Feature Highlights */}
      <div className="mt-16 text-center">
        <p className="text-foreground-muted text-base mb-6">
          Kana can help you with:
        </p>
        <div className="flex flex-wrap justify-center gap-3">
          <span className="px-4 py-2 bg-gradient-to-r from-background-secondary to-background-tertiary rounded-full text-foreground-secondary text-sm border border-border hover:border-border-light transition-all duration-200 hover:scale-105">
            🎨 Image Generation
          </span>
          <span className="px-4 py-2 bg-gradient-to-r from-background-secondary to-background-tertiary rounded-full text-foreground-secondary text-sm border border-border hover:border-border-light transition-all duration-200 hover:scale-105">
            ✏️ Image Editing
          </span>
          <span className="px-4 py-2 bg-gradient-to-r from-background-secondary to-background-tertiary rounded-full text-foreground-secondary text-sm border border-border hover:border-border-light transition-all duration-200 hover:scale-105">
            🎬 Video Creation
          </span>
          <span className="px-4 py-2 bg-gradient-to-r from-background-secondary to-background-tertiary rounded-full text-foreground-secondary text-sm border border-border hover:border-border-light transition-all duration-200 hover:scale-105">
            🌐 Web Search
          </span>
          <span className="px-4 py-2 bg-gradient-to-r from-background-secondary to-background-tertiary rounded-full text-foreground-secondary text-sm border border-border hover:border-border-light transition-all duration-200 hover:scale-105">
            📊 Data Analysis
          </span>
          <span className="px-4 py-2 bg-gradient-to-r from-background-secondary to-background-tertiary rounded-full text-foreground-secondary text-sm border border-border hover:border-border-light transition-all duration-200 hover:scale-105">
            🧠 Deep Thinking
          </span>
        </div>
      </div>
    </div>
  )
}

export default WelcomeScreen
