'use client'

import React from 'react'
import { Brain, ChevronDown, Mic } from 'lucide-react'
import { cn } from '@/lib/utils'

interface WelcomeScreenProps {
  onSendMessage: (content: string) => void
  isThinkMode: boolean
  onToggleThinkMode: () => void
  currentModel: string
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onSendMessage,
  isThinkMode,
  onToggleThinkMode,
  currentModel
}) => {
  const suggestedQuestions = [
    "What would happen to the Earth's climate if the planet's axial tilt were 0 degrees?",
    "Can you solve the \"infinity long\" problem?",
    "Can you create an awesome looking 3D fractal animation?",
    "Can you create a rocket simulation for a mission to mars using real orbital mechanics?"
  ]

  const handleQuestionClick = (question: string) => {
    onSendMessage(question)
  }

  return (
    <div className="flex flex-col items-center justify-center h-full px-6 py-12">
      {/* <PERSON>na <PERSON> and Title */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-6">
          <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
            <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full"></div>
            </div>
          </div>
        </div>
        <h1 className="text-4xl font-bold text-foreground mb-2">Kana</h1>
        <p className="text-lg text-foreground-secondary">Know more, Be more</p>
      </div>

      {/* Main Input Area */}
      <div className="w-full max-w-2xl mb-8">
        <div className="relative">
          <input
            type="text"
            placeholder="What do you want to know?"
            className="w-full px-6 py-4 bg-chat-input-bg border border-chat-input-border rounded-2xl text-foreground placeholder-foreground-muted focus:outline-none focus:border-chat-input-focus transition-colors text-lg"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                handleQuestionClick(e.currentTarget.value)
                e.currentTarget.value = ''
              }
            }}
          />
          
          {/* Input Controls */}
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-3">
            {/* Think Mode Toggle */}
            <button
              onClick={onToggleThinkMode}
              className={cn(
                "flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors",
                isThinkMode
                  ? "bg-kana-accent text-white"
                  : "bg-background-secondary hover:bg-background-hover text-foreground-secondary"
              )}
            >
              <Brain className="w-4 h-4" />
              <span className="text-sm">Think</span>
            </button>

            {/* Model Selector */}
            <div className="flex items-center space-x-2 px-3 py-2 bg-background-secondary rounded-lg">
              <span className="text-sm text-foreground">{currentModel}</span>
              <ChevronDown className="w-4 h-4 text-foreground-secondary" />
            </div>

            {/* Voice Input */}
            <button className="p-2 hover:bg-background-hover rounded-lg transition-colors">
              <Mic className="w-5 h-5 text-foreground-secondary" />
            </button>
          </div>
        </div>
      </div>

      {/* Suggested Questions */}
      <div className="w-full max-w-2xl space-y-3">
        {suggestedQuestions.map((question, index) => (
          <button
            key={index}
            onClick={() => handleQuestionClick(question)}
            className="w-full text-left p-4 bg-background-secondary hover:bg-background-hover rounded-lg transition-colors border border-border"
          >
            <p className="text-foreground-secondary text-sm leading-relaxed">
              {question}
            </p>
          </button>
        ))}
      </div>

      {/* Feature Highlights */}
      <div className="mt-12 text-center">
        <p className="text-foreground-muted text-sm mb-4">
          Kana can help you with:
        </p>
        <div className="flex flex-wrap justify-center gap-4 text-xs">
          <span className="px-3 py-1 bg-background-secondary rounded-full text-foreground-secondary">
            🎨 Image Generation
          </span>
          <span className="px-3 py-1 bg-background-secondary rounded-full text-foreground-secondary">
            ✏️ Image Editing
          </span>
          <span className="px-3 py-1 bg-background-secondary rounded-full text-foreground-secondary">
            🎬 Video Creation
          </span>
          <span className="px-3 py-1 bg-background-secondary rounded-full text-foreground-secondary">
            🌐 Web Search
          </span>
          <span className="px-3 py-1 bg-background-secondary rounded-full text-foreground-secondary">
            📊 Data Analysis
          </span>
          <span className="px-3 py-1 bg-background-secondary rounded-full text-foreground-secondary">
            🧠 Deep Thinking
          </span>
        </div>
      </div>
    </div>
  )
}

export default WelcomeScreen
