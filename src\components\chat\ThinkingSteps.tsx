'use client'

import React from 'react'
import { ChevronDown, ChevronUp, Brain } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ThinkingStep } from '@/types'

interface ThinkingStepsProps {
  steps: ThinkingStep[]
  isExpanded: boolean
  onToggle: () => void
  duration?: string
}

const ThinkingSteps: React.FC<ThinkingStepsProps> = ({ 
  steps, 
  isExpanded, 
  onToggle, 
  duration = "22s" 
}) => {
  return (
    <div className="mb-4">
      {/* Thinking Header */}
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-3 bg-thinking-bg border border-thinking-border rounded-lg hover:bg-background-hover transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Brain className="w-4 h-4 text-thinking-text" />
          <span className="text-sm text-thinking-text font-medium">
            Thinking {duration}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-xs text-thinking-text">
            {isExpanded ? 'Collapse details' : 'Expand for details'}
          </span>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-thinking-text" />
          ) : (
            <ChevronDown className="w-4 h-4 text-thinking-text" />
          )}
        </div>
      </button>

      {/* Thinking Content */}
      {isExpanded && (
        <div className="mt-2 p-4 bg-thinking-bg border border-thinking-border rounded-lg">
          <div className="space-y-3">
            {steps.map((step, index) => (
              <div key={step.id} className="flex space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-thinking-border rounded-full flex items-center justify-center">
                  <span className="text-xs text-thinking-text font-medium">
                    {index + 1}
                  </span>
                </div>
                <div className="flex-1">
                  <p className="text-sm text-thinking-text leading-relaxed">
                    {step.content}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          {/* Thinking Process Visualization */}
          <div className="mt-4 pt-3 border-t border-thinking-border">
            <div className="text-xs text-thinking-text space-y-1">
              <p>
                <span className="font-medium">Process:</span> Analyzing request → 
                Considering context → Formulating response
              </p>
              <p>
                <span className="font-medium">Model:</span> Kana 3 Pro (Think Mode)
              </p>
              <p>
                <span className="font-medium">Duration:</span> {duration}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ThinkingSteps
