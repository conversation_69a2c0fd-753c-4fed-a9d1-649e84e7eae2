'use client'

import React, { useState } from 'react'
import { 
  Search, 
  MessageSquare, 
  Mic, 
  FileText, 
  CheckSquare, 
  FolderOpen, 
  Plus, 
  Download,
  History,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { SidebarItem } from '@/types'

interface SidebarProps {
  isCollapsed: boolean
  onToggleCollapse: () => void
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggleCollapse }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [conversations] = useState<SidebarItem[]>([
    {
      id: '1',
      type: 'conversation',
      title: 'YouTube Video Creation: C...',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24) // 1 day ago
    },
    {
      id: '2', 
      type: 'conversation',
      title: 'Grok 4: Pricing and Altern...',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2) // 2 days ago
    },
    {
      id: '3',
      type: 'conversation', 
      title: 'The Entity in Mission: Imp...',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3) // 3 days ago
    },
    {
      id: '4',
      type: 'conversation',
      title: 'Daily Casual: Trendy Art A...',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 4) // 4 days ago
    }
  ])

  const navigationItems = [
    { icon: Search, label: 'Search', shortcut: 'Ctrl+K' },
    { icon: MessageSquare, label: 'Chat', isActive: true },
    { icon: Mic, label: 'Voice' },
    { icon: FileText, label: 'Files' },
    { icon: CheckSquare, label: 'Tasks' },
    { icon: FolderOpen, label: 'Projects' }
  ]

  const workspaceItems = [
    { icon: Plus, label: 'New Workspace', variant: 'primary' },
    { icon: Download, label: 'New Workspace', variant: 'secondary' }
  ]

  return (
    <div className={cn(
      "flex flex-col h-full bg-background-secondary border-r border-border transition-all duration-300",
      isCollapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
              <div className="w-4 h-4 bg-black rounded-full"></div>
            </div>
            <span className="text-foreground font-medium">Kana</span>
          </div>
        )}
        <button
          onClick={onToggleCollapse}
          className="p-1 hover:bg-background-hover rounded-md transition-colors"
        >
          {isCollapsed ? (
            <ChevronRight className="w-4 h-4 text-foreground-secondary" />
          ) : (
            <ChevronLeft className="w-4 h-4 text-foreground-secondary" />
          )}
        </button>
      </div>

      {/* Search */}
      {!isCollapsed && (
        <div className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-foreground-muted" />
            <input
              type="text"
              placeholder="Search Ctrl+K"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background-tertiary border border-border rounded-lg text-foreground placeholder-foreground-muted focus:outline-none focus:border-kana-accent transition-colors"
            />
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto">
        <nav className="p-2">
          {navigationItems.map((item, index) => (
            <button
              key={index}
              className={cn(
                "w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors text-left",
                item.isActive 
                  ? "bg-background-hover text-foreground" 
                  : "text-foreground-secondary hover:bg-background-hover hover:text-foreground"
              )}
            >
              <item.icon className="w-4 h-4 flex-shrink-0" />
              {!isCollapsed && (
                <>
                  <span className="flex-1">{item.label}</span>
                  {item.shortcut && (
                    <span className="text-xs text-foreground-muted">{item.shortcut}</span>
                  )}
                </>
              )}
            </button>
          ))}
        </nav>

        {/* Workspaces */}
        {!isCollapsed && (
          <div className="p-2 border-t border-border">
            {workspaceItems.map((item, index) => (
              <button
                key={index}
                className={cn(
                  "w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors text-left",
                  item.variant === 'primary' 
                    ? "text-kana-accent hover:bg-background-hover" 
                    : "text-info hover:bg-background-hover"
                )}
              >
                <item.icon className="w-4 h-4 flex-shrink-0" />
                <span className="flex-1">{item.label}</span>
              </button>
            ))}
            
            <div className="mt-4">
              <button className="w-full text-left px-3 py-1 text-foreground-secondary hover:text-foreground transition-colors">
                See all
              </button>
            </div>
          </div>
        )}

        {/* History */}
        {!isCollapsed && (
          <div className="p-2 border-t border-border">
            <div className="flex items-center space-x-2 px-3 py-2 text-foreground-secondary">
              <History className="w-4 h-4" />
              <span>History</span>
            </div>
            
            <div className="space-y-1">
              <div className="px-3 py-1 text-foreground-secondary text-sm">This Week</div>
              {conversations.map((conversation) => (
                <button
                  key={conversation.id}
                  className="w-full text-left px-3 py-2 text-foreground-secondary hover:text-foreground hover:bg-background-hover rounded-lg transition-colors text-sm truncate"
                >
                  {conversation.title}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* User Profile */}
      <div className="p-4 border-t border-border">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
            U
          </div>
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <div className="text-foreground text-sm font-medium truncate">User</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Sidebar
