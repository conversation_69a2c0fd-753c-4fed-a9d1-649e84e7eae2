export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  isThinking?: boolean
  thinkingSteps?: ThinkingStep[]
  functionCalls?: FunctionCall[]
  attachments?: Attachment[]
}

export interface ThinkingStep {
  id: string
  content: string
  timestamp: Date
}

export interface FunctionCall {
  id: string
  name: string
  arguments: Record<string, any>
  result?: any
  status: 'pending' | 'success' | 'error'
  timestamp: Date
}

export interface Attachment {
  id: string
  type: 'image' | 'file' | 'url'
  name: string
  url: string
  size?: number
  mimeType?: string
}

export interface Conversation {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  model: AIModel
  isThinkMode: boolean
}

export interface AIModel {
  id: string
  name: string
  provider: 'gemini' | 'a4f'
  type: 'chat' | 'image' | 'video'
  maxTokens?: number
  temperature?: number
}

export interface ChatSettings {
  model: AIModel
  temperature: number
  maxTokens: number
  isThinkMode: boolean
  enableFunctionCalling: boolean
}

export interface ImageGenerationParams {
  prompt: string
  model: string
  n?: number
  size?: string
  quality?: string
}

export interface ImageEditParams {
  image: File
  prompt: string
  model: string
}

export interface VideoGenerationParams {
  prompt: string
  model: string
  ratio?: string
  quality?: string
  duration?: number
}

export interface WebSearchParams {
  query: string
  maxResults?: number
}

export interface WebExtractParams {
  urls: string[]
  extractDepth?: 'basic' | 'advanced'
  format?: 'text' | 'markdown'
  includeImages?: boolean
  includeFavicon?: boolean
}

export interface WebCrawlParams {
  url: string
  extractDepth?: 'basic' | 'advanced'
  format?: 'text' | 'markdown'
  categories?: string[]
  selectPaths?: string[]
  selectDomains?: string[]
  excludePaths?: string[]
  excludeDomains?: string[]
  includeImages?: boolean
  includeFavicon?: boolean
}

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface SidebarItem {
  id: string
  type: 'conversation' | 'workspace' | 'section'
  title: string
  icon?: string
  isActive?: boolean
  children?: SidebarItem[]
  timestamp?: Date
}

export interface UserPreferences {
  theme: 'dark' | 'light' | 'system'
  fontSize: 'small' | 'medium' | 'large'
  codeTheme: string
  autoSave: boolean
  showTimestamps: boolean
  enableNotifications: boolean
  defaultModel: string
  enableThinkMode: boolean
}

export interface AppState {
  currentConversation: Conversation | null
  conversations: Conversation[]
  isLoading: boolean
  error: string | null
  settings: ChatSettings
  preferences: UserPreferences
  sidebarCollapsed: boolean
}

// Function calling types
export type FunctionName = 
  | 'create_image'
  | 'edit_image'
  | 'generate_video'
  | 'web_search'
  | 'web_extract'
  | 'web_crawl'

export interface FunctionDefinition {
  name: FunctionName
  description: string
  parameters: {
    type: 'object'
    properties: Record<string, {
      type: string
      description: string
      enum?: string[]
    }>
    required: string[]
  }
}

// API Keys interface
export interface APIKeys {
  gemini: string
  a4f: string
  tavily: string
}

// Component props types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

export interface InputProps extends BaseComponentProps {
  type?: string
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  disabled?: boolean
  error?: string
  autoFocus?: boolean
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
}
