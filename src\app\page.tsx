'use client'

import React, { useState } from 'react'
import Sidebar from '@/components/layout/Sidebar'
import ChatInterface from '@/components/chat/ChatInterface'

export default function Home() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [chatKey, setChatKey] = useState(0) // Force re-render of ChatInterface

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const handleNewChat = () => {
    setChatKey(prev => prev + 1) // This will force ChatInterface to remount
  }

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <Sidebar
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={handleToggleSidebar}
        onNewChat={handleNewChat}
      />
      <main className="flex-1 flex flex-col overflow-hidden">
        <ChatInterface key={chatKey} />
      </main>
    </div>
  )
}
