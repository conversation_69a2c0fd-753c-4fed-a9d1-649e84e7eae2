'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, ChevronDown, ChevronUp, <PERSON><PERSON>, ThumbsUp, ThumbsDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Message } from '@/types'
import ReactMarkdown from 'react-markdown'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import ThinkingSteps from './ThinkingSteps'
import ImageDisplay from '@/components/ui/ImageDisplay'
import VideoDisplay from '@/components/ui/VideoDisplay'

interface ChatMessageProps {
  message: Message
  isLoading?: boolean
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, isLoading }) => {
  const [showThinking, setShowThinking] = useState(false)
  const isUser = message.role === 'user'

  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.content)
  }

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className={cn(
      "flex space-x-4 group",
      isUser ? "justify-end" : "justify-start"
    )}>
      {!isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
            <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-full"></div>
            </div>
          </div>
        </div>
      )}

      <div className={cn(
        "flex-1 max-w-3xl",
        isUser ? "flex justify-end" : ""
      )}>
        <div className={cn(
          "rounded-lg px-4 py-3",
          isUser 
            ? "bg-chat-user-bg text-foreground ml-12" 
            : "bg-chat-ai-bg text-foreground"
        )}>
          {/* Thinking Steps */}
          {message.isThinking && message.thinkingSteps && (
            <ThinkingSteps 
              steps={message.thinkingSteps}
              isExpanded={showThinking}
              onToggle={() => setShowThinking(!showThinking)}
            />
          )}

          {/* Message Content */}
          <div className="prose prose-invert max-w-none">
            {isUser ? (
              <p className="text-foreground whitespace-pre-wrap">{message.content}</p>
            ) : (
              <ReactMarkdown
                components={{
                  code({ node, inline, className, children, ...props }) {
                    const match = /language-(\w+)/.exec(className || '')
                    return !inline && match ? (
                      <SyntaxHighlighter
                        style={oneDark}
                        language={match[1]}
                        PreTag="div"
                        className="rounded-md"
                        {...props}
                      >
                        {String(children).replace(/\n$/, '')}
                      </SyntaxHighlighter>
                    ) : (
                      <code className="bg-background-tertiary px-1 py-0.5 rounded text-sm" {...props}>
                        {children}
                      </code>
                    )
                  },
                  p({ children }) {
                    return <p className="text-foreground mb-2 last:mb-0">{children}</p>
                  },
                  ul({ children }) {
                    return <ul className="text-foreground list-disc list-inside mb-2">{children}</ul>
                  },
                  ol({ children }) {
                    return <ol className="text-foreground list-decimal list-inside mb-2">{children}</ol>
                  },
                  li({ children }) {
                    return <li className="text-foreground mb-1">{children}</li>
                  },
                  h1({ children }) {
                    return <h1 className="text-foreground text-xl font-bold mb-2">{children}</h1>
                  },
                  h2({ children }) {
                    return <h2 className="text-foreground text-lg font-bold mb-2">{children}</h2>
                  },
                  h3({ children }) {
                    return <h3 className="text-foreground text-md font-bold mb-2">{children}</h3>
                  },
                  blockquote({ children }) {
                    return (
                      <blockquote className="border-l-4 border-border-light pl-4 text-foreground-secondary italic mb-2">
                        {children}
                      </blockquote>
                    )
                  },
                  a({ href, children }) {
                    return (
                      <a
                        href={href}
                        className="text-kana-accent hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {children}
                      </a>
                    )
                  }
                }}
              >
                {message.content}
              </ReactMarkdown>
            )}
          </div>

          {/* Function Call Results */}
          {message.functionCalls && message.functionCalls.length > 0 && (
            <div className="mt-4 space-y-3">
              {message.functionCalls.map((call) => (
                <div key={call.id} className="bg-background-tertiary border border-border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-foreground">
                      {call.name === 'create_image' && '🎨 Image Generated'}
                      {call.name === 'generate_video' && '🎬 Video Generated'}
                      {call.name === 'edit_image' && '✏️ Image Edited'}
                      {call.name === 'web_search' && '🔍 Web Search'}
                      {call.name === 'web_extract' && '📄 Web Extract'}
                      {call.name === 'web_crawl' && '🕷️ Web Crawl'}
                    </span>
                    <span className={cn(
                      "text-xs px-2 py-1 rounded",
                      call.status === 'success' ? "bg-success/20 text-success" :
                      call.status === 'pending' ? "bg-warning/20 text-warning" :
                      "bg-error/20 text-error"
                    )}>
                      {call.status}
                    </span>
                  </div>

                  {/* Display results based on function type */}
                  {call.result && call.status === 'success' && (
                    <div className="mt-2">
                      {(call.name === 'create_image' || call.name === 'edit_image') && call.result.data && (
                        <div className="space-y-2">
                          {call.result.data.map((item: any, index: number) => (
                            <ImageDisplay
                              key={index}
                              src={item.url}
                              alt={`Generated image ${index + 1}`}
                              caption={call.arguments?.prompt}
                              className="max-w-md"
                            />
                          ))}
                        </div>
                      )}

                      {call.name === 'generate_video' && call.result.url && (
                        <VideoDisplay
                          src={call.result.url}
                          caption={call.arguments?.prompt}
                          className="max-w-md"
                        />
                      )}

                      {(call.name === 'web_search' || call.name === 'web_extract' || call.name === 'web_crawl') && (
                        <div className="text-sm text-foreground-secondary">
                          <pre className="whitespace-pre-wrap">{JSON.stringify(call.result, null, 2)}</pre>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-3 space-y-2">
              {message.attachments.map((attachment) => (
                <div key={attachment.id} className="flex items-center space-x-2">
                  {attachment.type === 'image' ? (
                    <img 
                      src={attachment.url} 
                      alt={attachment.name}
                      className="max-w-xs rounded-lg"
                    />
                  ) : (
                    <div className="flex items-center space-x-2 bg-background-tertiary px-3 py-2 rounded-lg">
                      <span className="text-sm text-foreground">{attachment.name}</span>
                      {attachment.size && (
                        <span className="text-xs text-foreground-secondary">
                          ({(attachment.size / 1024).toFixed(1)} KB)
                        </span>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Message Actions */}
          {!isUser && (
            <div className="flex items-center justify-between mt-3 pt-2 border-t border-border opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleCopyMessage}
                  className="p-1 hover:bg-background-hover rounded transition-colors"
                  title="Copy message"
                >
                  <Copy className="w-4 h-4 text-foreground-secondary" />
                </button>
                <button
                  className="p-1 hover:bg-background-hover rounded transition-colors"
                  title="Like"
                >
                  <ThumbsUp className="w-4 h-4 text-foreground-secondary" />
                </button>
                <button
                  className="p-1 hover:bg-background-hover rounded transition-colors"
                  title="Dislike"
                >
                  <ThumbsDown className="w-4 h-4 text-foreground-secondary" />
                </button>
              </div>
              
              <span className="text-xs text-foreground-muted">
                {formatTimestamp(message.timestamp)}
              </span>
            </div>
          )}
        </div>
      </div>

      {isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
        </div>
      )}
    </div>
  )
}

export default ChatMessage
