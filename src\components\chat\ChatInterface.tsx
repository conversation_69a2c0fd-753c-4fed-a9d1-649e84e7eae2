'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  Send, 
  Paperclip, 
  Mic, 
  Image as ImageIcon, 
  Video, 
  Globe, 
  User, 
  Settings,
  Share,
  MoreHorizontal,
  Lock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Message } from '@/types'
import ChatMessage from './ChatMessage'
import ChatInput from './ChatInput'
import WelcomeScreen from './WelcomeScreen'
import Modal from '@/components/ui/Modal'
import APIKeySettings from '@/components/settings/APIKeySettings'

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isThinkMode, setIsThinkMode] = useState(false)
  const [currentModel, setCurrentModel] = useState('Kana 3')
  const [showSettings, setShowSettings] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!content.trim() && !attachments?.length) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: new Date(),
      attachments: attachments?.map(file => ({
        id: Date.now().toString() + Math.random(),
        type: file.type.startsWith('image/') ? 'image' : 'file',
        name: file.name,
        url: URL.createObjectURL(file),
        size: file.size,
        mimeType: file.type
      }))
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // Get API keys from localStorage
      const savedKeys = localStorage.getItem('kana-api-keys')
      const apiKeys = savedKeys ? JSON.parse(savedKeys) : {}

      // Call the chat API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          isThinkMode,
          model: isThinkMode ? 'gemini-2.5-pro' : 'gemini-2.5-flash',
          apiKeys
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      const data = await response.json()

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.content,
        timestamp: new Date(),
        isThinking: isThinkMode,
        thinkingSteps: isThinkMode ? [
          {
            id: '1',
            content: 'Analyzing the user\'s request and context...',
            timestamp: new Date()
          },
          {
            id: '2',
            content: 'Processing information and formulating response...',
            timestamp: new Date()
          },
          {
            id: '3',
            content: 'Reviewing and refining the answer...',
            timestamp: new Date()
          }
        ] : undefined,
        functionCalls: data.functionCalls?.map((call: any) => ({
          id: Date.now().toString() + Math.random(),
          name: call.name,
          arguments: call.args || {},
          result: call.response,
          status: 'success',
          timestamp: new Date()
        }))
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      console.error('Error sending message:', error)

      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error while processing your request. Please try again.',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleThinkMode = () => {
    setIsThinkMode(!isThinkMode)
    setCurrentModel(isThinkMode ? 'Kana 3' : 'Kana 3 Pro')
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <header className="flex items-center justify-between px-6 py-4 border-b border-border bg-background">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-full"></div>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-foreground">Kana</h1>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Lock className="w-4 h-4 text-foreground-secondary" />
            <span className="text-sm text-foreground-secondary">Private</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-background-hover rounded-lg transition-colors">
              <Share className="w-4 h-4 text-foreground-secondary" />
            </button>
            <button
              onClick={() => setShowSettings(true)}
              className="p-2 hover:bg-background-hover rounded-lg transition-colors"
            >
              <Settings className="w-4 h-4 text-foreground-secondary" />
            </button>
            <button className="p-2 hover:bg-background-hover rounded-lg transition-colors">
              <MoreHorizontal className="w-4 h-4 text-foreground-secondary" />
            </button>
          </div>
        </div>
      </header>

      {/* Chat Area */}
      <div className="flex-1 overflow-hidden">
        {messages.length === 0 ? (
          <WelcomeScreen 
            onSendMessage={handleSendMessage}
            isThinkMode={isThinkMode}
            onToggleThinkMode={handleToggleThinkMode}
            currentModel={currentModel}
          />
        ) : (
          <div className="h-full overflow-y-auto px-6 py-4">
            <div className="max-w-4xl mx-auto space-y-6">
              {messages.map((message) => (
                <ChatMessage 
                  key={message.id} 
                  message={message}
                  isLoading={isLoading && message === messages[messages.length - 1]}
                />
              ))}
              {isLoading && (
                <div className="flex items-center space-x-2 text-foreground-secondary">
                  <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse delay-75"></div>
                  <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse delay-150"></div>
                  <span className="text-sm">Kana is thinking...</span>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="border-t border-border bg-background">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <ChatInput 
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            isThinkMode={isThinkMode}
            onToggleThinkMode={handleToggleThinkMode}
            currentModel={currentModel}
          />
        </div>
      </div>

      {/* Settings Modal */}
      <Modal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        title="Settings"
        size="lg"
      >
        <APIKeySettings />
      </Modal>
    </div>
  )
}

export default ChatInterface
