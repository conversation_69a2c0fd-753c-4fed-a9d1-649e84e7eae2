'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  Send, 
  Paperclip, 
  Mic, 
  Image as ImageIcon, 
  Video, 
  Globe, 
  User, 
  Settings,
  Share,
  MoreHorizontal,
  Lock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Message } from '@/types'
import ChatMessage from './ChatMessage'
import ChatInput from './ChatInput'
import WelcomeScreen from './WelcomeScreen'
import Modal from '@/components/ui/Modal'
import APIKeySettings from '@/components/settings/APIKeySettings'

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isThinkMode, setIsThinkMode] = useState(false)
  const [currentModel, setCurrentModel] = useState('Kana 3')
  const [showSettings, setShowSettings] = useState(false)
  const [conversationId, setConversationId] = useState<string>('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load conversation history on mount
  useEffect(() => {
    const savedConversation = localStorage.getItem('kana-current-conversation')
    if (savedConversation) {
      try {
        const parsed = JSON.parse(savedConversation)
        setMessages(parsed.messages || [])
        setConversationId(parsed.id || '')
      } catch (error) {
        console.error('Error loading conversation:', error)
      }
    } else {
      // Create new conversation
      const newId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      setConversationId(newId)
    }
  }, [])

  // Save conversation history whenever messages change
  useEffect(() => {
    if (conversationId && messages.length > 0) {
      const conversation = {
        id: conversationId,
        messages,
        timestamp: new Date().toISOString(),
        title: messages[0]?.content.slice(0, 50) + '...' || 'New Conversation'
      }

      localStorage.setItem('kana-current-conversation', JSON.stringify(conversation))

      // Also save to conversation history
      const history = JSON.parse(localStorage.getItem('kana-conversation-history') || '[]')
      const existingIndex = history.findIndex((conv: any) => conv.id === conversationId)

      if (existingIndex >= 0) {
        history[existingIndex] = conversation
      } else {
        history.unshift(conversation)
      }

      // Keep only last 50 conversations
      if (history.length > 50) {
        history.splice(50)
      }

      localStorage.setItem('kana-conversation-history', JSON.stringify(history))
    }
  }, [messages, conversationId])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const startNewConversation = () => {
    setMessages([])
    const newId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    setConversationId(newId)
    localStorage.removeItem('kana-current-conversation')
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!content.trim() && !attachments?.length) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: new Date(),
      attachments: attachments?.map(file => ({
        id: Date.now().toString() + Math.random(),
        type: file.type.startsWith('image/') ? 'image' : 'file',
        name: file.name,
        url: URL.createObjectURL(file),
        size: file.size,
        mimeType: file.type
      }))
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // Get API keys from localStorage
      const savedKeys = localStorage.getItem('kana-api-keys')
      const apiKeys = savedKeys ? JSON.parse(savedKeys) : {}

      // Create initial AI message for streaming
      const aiMessageId = (Date.now() + 1).toString()
      const aiMessage: Message = {
        id: aiMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        isThinking: isThinkMode,
        thinkingSteps: isThinkMode ? [
          {
            id: '1',
            content: 'Analyzing the user\'s request and context...',
            timestamp: new Date()
          },
          {
            id: '2',
            content: 'Processing information and formulating response...',
            timestamp: new Date()
          },
          {
            id: '3',
            content: 'Reviewing and refining the answer...',
            timestamp: new Date()
          }
        ] : undefined,
        functionCalls: []
      }

      setMessages(prev => [...prev, aiMessage])

      // Call the chat API with streaming
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          isThinkMode,
          model: isThinkMode ? 'gemini-2.5-pro' : 'gemini-2.5-flash',
          apiKeys
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      // Handle streaming response
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        let buffer = ''

        while (true) {
          const { done, value } = await reader.read()

          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.type === 'content') {
                  // Update message content
                  setMessages(prev => prev.map(msg =>
                    msg.id === aiMessageId
                      ? { ...msg, content: msg.content + data.content }
                      : msg
                  ))
                } else if (data.type === 'function_call') {
                  // Add function call to message
                  setMessages(prev => prev.map(msg =>
                    msg.id === aiMessageId
                      ? {
                          ...msg,
                          functionCalls: [
                            ...(msg.functionCalls || []),
                            {
                              id: Date.now().toString() + Math.random(),
                              name: data.name,
                              arguments: data.args || {},
                              result: null,
                              status: 'pending',
                              timestamp: new Date()
                            }
                          ]
                        }
                      : msg
                  ))
                } else if (data.type === 'function_result') {
                  // Update function call result
                  setMessages(prev => prev.map(msg =>
                    msg.id === aiMessageId
                      ? {
                          ...msg,
                          functionCalls: msg.functionCalls?.map(call =>
                            call.name === data.name
                              ? { ...call, result: data.result, status: 'success' }
                              : call
                          )
                        }
                      : msg
                  ))
                } else if (data.type === 'done') {
                  setIsLoading(false)
                  break
                } else if (data.type === 'error') {
                  throw new Error(data.error)
                }
              } catch (parseError) {
                console.error('Error parsing streaming data:', parseError)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error)

      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error while processing your request. Please try again.',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, errorMessage])
      setIsLoading(false)
    }
  }

  const handleToggleThinkMode = () => {
    setIsThinkMode(!isThinkMode)
    setCurrentModel(isThinkMode ? 'Kana 3' : 'Kana 3 Pro')
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <header className="flex items-center justify-between px-6 py-4 border-b border-border bg-background">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-full"></div>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-foreground">Kana</h1>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Lock className="w-4 h-4 text-foreground-secondary" />
            <span className="text-sm text-foreground-secondary">Private</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-background-hover rounded-lg transition-colors">
              <Share className="w-4 h-4 text-foreground-secondary" />
            </button>
            <button
              onClick={() => setShowSettings(true)}
              className="p-2 hover:bg-background-hover rounded-lg transition-colors"
            >
              <Settings className="w-4 h-4 text-foreground-secondary" />
            </button>
            <button className="p-2 hover:bg-background-hover rounded-lg transition-colors">
              <MoreHorizontal className="w-4 h-4 text-foreground-secondary" />
            </button>
          </div>
        </div>
      </header>

      {/* Chat Area */}
      <div className="flex-1 overflow-hidden">
        {messages.length === 0 ? (
          <WelcomeScreen 
            onSendMessage={handleSendMessage}
            isThinkMode={isThinkMode}
            onToggleThinkMode={handleToggleThinkMode}
            currentModel={currentModel}
          />
        ) : (
          <div className="h-full overflow-y-auto px-6 py-4">
            <div className="max-w-4xl mx-auto space-y-6">
              {messages.map((message) => (
                <ChatMessage 
                  key={message.id} 
                  message={message}
                  isLoading={isLoading && message === messages[messages.length - 1]}
                />
              ))}
              {isLoading && (
                <div className="flex items-center space-x-2 text-foreground-secondary">
                  <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse delay-75"></div>
                  <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse delay-150"></div>
                  <span className="text-sm">Kana is thinking...</span>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="border-t border-border bg-background">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <ChatInput 
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            isThinkMode={isThinkMode}
            onToggleThinkMode={handleToggleThinkMode}
            currentModel={currentModel}
          />
        </div>
      </div>

      {/* Settings Modal */}
      <Modal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        title="Settings"
        size="lg"
      >
        <APIKeySettings />
      </Modal>
    </div>
  )
}

export default ChatInterface
