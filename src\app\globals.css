@import "tailwindcss";

:root {
  /* Kana Dark Theme - Grok-inspired color palette */
  --background: #000000;
  --background-secondary: #111111;
  --background-tertiary: #1a1a1a;
  --background-hover: rgba(255, 255, 255, 0.05);
  --foreground: #ffffff;
  --foreground-secondary: #a1a1aa;
  --foreground-muted: #71717a;
  --border: #27272a;
  --border-light: #3f3f46;
  --accent: #ffffff;
  --accent-secondary: #f4f4f5;
  --success: #22c55e;
  --warning: #eab308;
  --error: #ef4444;
  --info: #3b82f6;

  /* Kana brand colors */
  --kana-primary: #ffffff;
  --kana-secondary: #a1a1aa;
  --kana-accent: #6366f1;

  /* Chat specific colors */
  --chat-user-bg: #18181b;
  --chat-ai-bg: transparent;
  --chat-input-bg: #18181b;
  --chat-input-border: #27272a;
  --chat-input-focus: #6366f1;

  /* Thinking mode colors */
  --thinking-bg: #0f0f0f;
  --thinking-border: #27272a;
  --thinking-text: #a1a1aa;
}

@theme inline {
  --color-background: var(--background);
  --color-background-secondary: var(--background-secondary);
  --color-background-tertiary: var(--background-tertiary);
  --color-background-hover: var(--background-hover);
  --color-foreground: var(--foreground);
  --color-foreground-secondary: var(--foreground-secondary);
  --color-foreground-muted: var(--foreground-muted);
  --color-border: var(--border);
  --color-border-light: var(--border-light);
  --color-accent: var(--accent);
  --color-accent-secondary: var(--accent-secondary);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);
  --color-kana-primary: var(--kana-primary);
  --color-kana-secondary: var(--kana-secondary);
  --color-kana-accent: var(--kana-accent);
  --color-chat-user-bg: var(--chat-user-bg);
  --color-chat-ai-bg: var(--chat-ai-bg);
  --color-chat-input-bg: var(--chat-input-bg);
  --color-chat-input-border: var(--chat-input-border);
  --color-chat-input-focus: var(--chat-input-focus);
  --color-thinking-bg: var(--thinking-bg);
  --color-thinking-border: var(--thinking-border);
  --color-thinking-text: var(--thinking-text);
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono: "SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas, "Courier New", monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  overflow: hidden;
}

body {
  height: 100%;
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-light);
}

/* Selection */
::selection {
  background: var(--kana-accent);
  color: white;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--kana-accent);
  outline-offset: 2px;
}

/* Improved button styles */
button {
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Improved input styles */
input, textarea {
  transition: all 0.2s ease-in-out;
}

/* Smooth animations */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading animation */
.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Better shadows */
.shadow-glow {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #ffffff 0%, #a1a1aa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass effect */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
