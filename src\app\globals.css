@import "tailwindcss";

:root {
  /* Kana Dark Theme - Grok-inspired color palette */
  --background: #0a0a0a;
  --background-secondary: #1a1a1a;
  --background-tertiary: #2a2a2a;
  --background-hover: #333333;
  --foreground: #ffffff;
  --foreground-secondary: #b3b3b3;
  --foreground-muted: #666666;
  --border: #333333;
  --border-light: #404040;
  --accent: #ffffff;
  --accent-secondary: #f5f5f5;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Kana brand colors */
  --kana-primary: #ffffff;
  --kana-secondary: #888888;
  --kana-accent: #4f46e5;

  /* Chat specific colors */
  --chat-user-bg: #2a2a2a;
  --chat-ai-bg: #1a1a1a;
  --chat-input-bg: #1a1a1a;
  --chat-input-border: #333333;
  --chat-input-focus: #4f46e5;

  /* Thinking mode colors */
  --thinking-bg: #1e1e1e;
  --thinking-border: #333333;
  --thinking-text: #888888;
}

@theme inline {
  --color-background: var(--background);
  --color-background-secondary: var(--background-secondary);
  --color-background-tertiary: var(--background-tertiary);
  --color-background-hover: var(--background-hover);
  --color-foreground: var(--foreground);
  --color-foreground-secondary: var(--foreground-secondary);
  --color-foreground-muted: var(--foreground-muted);
  --color-border: var(--border);
  --color-border-light: var(--border-light);
  --color-accent: var(--accent);
  --color-accent-secondary: var(--accent-secondary);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);
  --color-kana-primary: var(--kana-primary);
  --color-kana-secondary: var(--kana-secondary);
  --color-kana-accent: var(--kana-accent);
  --color-chat-user-bg: var(--chat-user-bg);
  --color-chat-ai-bg: var(--chat-ai-bg);
  --color-chat-input-bg: var(--chat-input-bg);
  --color-chat-input-border: var(--chat-input-border);
  --color-chat-input-focus: var(--chat-input-focus);
  --color-thinking-bg: var(--thinking-bg);
  --color-thinking-border: var(--thinking-border);
  --color-thinking-text: var(--thinking-text);
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono: "SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas, "Courier New", monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  overflow: hidden;
}

body {
  height: 100%;
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-light);
}

/* Selection */
::selection {
  background: var(--kana-accent);
  color: white;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--kana-accent);
  outline-offset: 2px;
}
