'use client'

import React, { useState } from 'react'
import { Download, ExternalLink, Play, Pause } from 'lucide-react'
import { cn } from '@/lib/utils'

interface VideoDisplayProps {
  src: string
  poster?: string
  caption?: string
  className?: string
  showControls?: boolean
}

const VideoDisplay: React.FC<VideoDisplayProps> = ({
  src,
  poster,
  caption,
  className,
  showControls = true
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(false)

  const handleDownload = async () => {
    try {
      const response = await fetch(src)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `kana-generated-video-${Date.now()}.mp4`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading video:', error)
    }
  }

  const handleOpenInNewTab = () => {
    window.open(src, '_blank')
  }

  const handleVideoLoad = () => {
    setIsLoading(false)
  }

  const handleVideoError = () => {
    setIsLoading(false)
    setError(true)
  }

  if (error) {
    return (
      <div className="bg-background-secondary border border-border rounded-lg p-4 text-center">
        <p className="text-foreground-secondary text-sm">Failed to load video</p>
        <button
          onClick={handleOpenInNewTab}
          className="mt-2 text-kana-accent hover:underline text-sm"
        >
          Open in new tab
        </button>
      </div>
    )
  }

  return (
    <div className={cn(
      "relative bg-background-secondary border border-border rounded-lg overflow-hidden group",
      className
    )}>
      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background-secondary z-10">
          <div className="animate-spin w-6 h-6 border-2 border-kana-accent border-t-transparent rounded-full"></div>
        </div>
      )}

      {/* Video */}
      <video
        src={src}
        poster={poster}
        controls
        onLoadedData={handleVideoLoad}
        onError={handleVideoError}
        className={cn(
          "w-full h-auto max-w-full transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
        preload="metadata"
      >
        Your browser does not support the video tag.
      </video>

      {/* Controls overlay */}
      {showControls && !isLoading && (
        <div className="absolute top-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={handleDownload}
            className="p-2 bg-black/50 hover:bg-black/70 rounded-lg transition-colors"
            title="Download video"
          >
            <Download className="w-4 h-4 text-white" />
          </button>
          <button
            onClick={handleOpenInNewTab}
            className="p-2 bg-black/50 hover:bg-black/70 rounded-lg transition-colors"
            title="Open in new tab"
          >
            <ExternalLink className="w-4 h-4 text-white" />
          </button>
        </div>
      )}

      {/* Caption */}
      {caption && (
        <div className="p-3 bg-background-tertiary border-t border-border">
          <p className="text-sm text-foreground-secondary">{caption}</p>
        </div>
      )}
    </div>
  )
}

export default VideoDisplay
