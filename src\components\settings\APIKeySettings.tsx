'use client'

import React, { useState, useEffect } from 'react'
import { Eye, EyeOff, Save, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface APIKeys {
  gemini: string
  a4f: string
  tavily: string
}

const APIKeySettings: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<APIKeys>({
    gemini: '',
    a4f: '',
    tavily: '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' // Pre-filled Tavily key
  })
  const [showKeys, setShowKeys] = useState({
    gemini: false,
    a4f: false,
    tavily: false
  })
  const [saved, setSaved] = useState(false)

  useEffect(() => {
    // Load API keys from localStorage
    const savedKeys = localStorage.getItem('kana-api-keys')
    if (savedKeys) {
      try {
        const parsed = JSON.parse(savedKeys)
        setApiKeys(prev => ({ ...prev, ...parsed }))
      } catch (error) {
        console.error('Error loading API keys:', error)
      }
    }
  }, [])

  const handleKeyChange = (key: keyof APIKeys, value: string) => {
    setApiKeys(prev => ({ ...prev, [key]: value }))
    setSaved(false)
  }

  const toggleShowKey = (key: keyof APIKeys) => {
    setShowKeys(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const handleSave = () => {
    localStorage.setItem('kana-api-keys', JSON.stringify(apiKeys))
    setSaved(true)
    setTimeout(() => setSaved(false), 2000)
  }

  const isKeyValid = (key: string) => key.length > 10

  return (
    <div className="max-w-2xl mx-auto p-6 bg-background-secondary rounded-lg border border-border">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-foreground mb-2">API Configuration</h2>
        <p className="text-foreground-secondary text-sm">
          Configure your API keys to enable all Kana features. Keys are stored locally in your browser.
        </p>
      </div>

      <div className="space-y-6">
        {/* Gemini API Key */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Google Gemini API Key
            <span className="text-error ml-1">*</span>
          </label>
          <div className="relative">
            <input
              type={showKeys.gemini ? 'text' : 'password'}
              value={apiKeys.gemini}
              onChange={(e) => handleKeyChange('gemini', e.target.value)}
              placeholder="Enter your Gemini API key"
              className="w-full px-4 py-3 pr-12 bg-background border border-border rounded-lg text-foreground placeholder-foreground-muted focus:outline-none focus:border-kana-accent transition-colors"
            />
            <button
              type="button"
              onClick={() => toggleShowKey('gemini')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground-secondary hover:text-foreground"
            >
              {showKeys.gemini ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          <p className="text-xs text-foreground-muted mt-1">
            Required for AI chat functionality. Get your key from{' '}
            <a 
              href="https://makersuite.google.com/app/apikey" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-kana-accent hover:underline"
            >
              Google AI Studio
            </a>
          </p>
        </div>

        {/* A4F API Key */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            A4F API Key
            <span className="text-error ml-1">*</span>
          </label>
          <div className="relative">
            <input
              type={showKeys.a4f ? 'text' : 'password'}
              value={apiKeys.a4f}
              onChange={(e) => handleKeyChange('a4f', e.target.value)}
              placeholder="Enter your A4F API key"
              className="w-full px-4 py-3 pr-12 bg-background border border-border rounded-lg text-foreground placeholder-foreground-muted focus:outline-none focus:border-kana-accent transition-colors"
            />
            <button
              type="button"
              onClick={() => toggleShowKey('a4f')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground-secondary hover:text-foreground"
            >
              {showKeys.a4f ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          <p className="text-xs text-foreground-muted mt-1">
            Required for image generation, editing, and video creation features.
          </p>
        </div>

        {/* Tavily API Key */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Tavily API Key
            <span className="text-success ml-1">✓</span>
          </label>
          <div className="relative">
            <input
              type={showKeys.tavily ? 'text' : 'password'}
              value={apiKeys.tavily}
              onChange={(e) => handleKeyChange('tavily', e.target.value)}
              placeholder="Enter your Tavily API key"
              className="w-full px-4 py-3 pr-12 bg-background border border-border rounded-lg text-foreground placeholder-foreground-muted focus:outline-none focus:border-kana-accent transition-colors"
            />
            <button
              type="button"
              onClick={() => toggleShowKey('tavily')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground-secondary hover:text-foreground"
            >
              {showKeys.tavily ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          <p className="text-xs text-foreground-muted mt-1">
            Required for web search and data extraction features. Pre-configured with demo key.
          </p>
        </div>
      </div>

      {/* Status and Save */}
      <div className="mt-8 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {!isKeyValid(apiKeys.gemini) && (
            <div className="flex items-center space-x-2 text-warning">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">Gemini API key required</span>
            </div>
          )}
          {!isKeyValid(apiKeys.a4f) && (
            <div className="flex items-center space-x-2 text-warning">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">A4F API key required</span>
            </div>
          )}
        </div>

        <button
          onClick={handleSave}
          className={cn(
            "flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors",
            saved
              ? "bg-success text-white"
              : "bg-kana-accent text-white hover:bg-kana-accent/90"
          )}
        >
          <Save className="w-4 h-4" />
          <span>{saved ? 'Saved!' : 'Save Keys'}</span>
        </button>
      </div>

      {/* Feature Status */}
      <div className="mt-6 p-4 bg-background border border-border rounded-lg">
        <h3 className="text-sm font-medium text-foreground mb-3">Feature Status</h3>
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="flex items-center justify-between">
            <span className="text-foreground-secondary">AI Chat</span>
            <span className={cn(
              "px-2 py-1 rounded",
              isKeyValid(apiKeys.gemini) 
                ? "bg-success/20 text-success" 
                : "bg-error/20 text-error"
            )}>
              {isKeyValid(apiKeys.gemini) ? 'Ready' : 'Needs Key'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-foreground-secondary">Image Generation</span>
            <span className={cn(
              "px-2 py-1 rounded",
              isKeyValid(apiKeys.a4f) 
                ? "bg-success/20 text-success" 
                : "bg-error/20 text-error"
            )}>
              {isKeyValid(apiKeys.a4f) ? 'Ready' : 'Needs Key'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-foreground-secondary">Video Generation</span>
            <span className={cn(
              "px-2 py-1 rounded",
              isKeyValid(apiKeys.a4f) 
                ? "bg-success/20 text-success" 
                : "bg-error/20 text-error"
            )}>
              {isKeyValid(apiKeys.a4f) ? 'Ready' : 'Needs Key'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-foreground-secondary">Web Search</span>
            <span className="px-2 py-1 rounded bg-success/20 text-success">
              Ready
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default APIKeySettings
