'use client'

import React, { useState } from 'react'
import { Download, ExternalLink, Eye, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ImageDisplayProps {
  src: string
  alt?: string
  caption?: string
  className?: string
  showControls?: boolean
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({
  src,
  alt = 'Generated image',
  caption,
  className,
  showControls = true
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [error, setError] = useState(false)

  const handleDownload = async () => {
    try {
      const response = await fetch(src)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `kana-generated-image-${Date.now()}.png`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading image:', error)
    }
  }

  const handleOpenInNewTab = () => {
    window.open(src, '_blank')
  }

  const handleImageLoad = () => {
    setIsLoading(false)
  }

  const handleImageError = () => {
    setIsLoading(false)
    setError(true)
  }

  if (error) {
    return (
      <div className="bg-background-secondary border border-border rounded-lg p-4 text-center">
        <p className="text-foreground-secondary text-sm">Failed to load image</p>
        <button
          onClick={handleOpenInNewTab}
          className="mt-2 text-kana-accent hover:underline text-sm"
        >
          Open in new tab
        </button>
      </div>
    )
  }

  return (
    <>
      <div className={cn(
        "relative bg-background-secondary border border-border rounded-lg overflow-hidden",
        className
      )}>
        {/* Loading state */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background-secondary">
            <div className="animate-spin w-6 h-6 border-2 border-kana-accent border-t-transparent rounded-full"></div>
          </div>
        )}

        {/* Image */}
        <img
          src={src}
          alt={alt}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={cn(
            "w-full h-auto max-w-full transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100"
          )}
        />

        {/* Controls overlay */}
        {showControls && !isLoading && (
          <div className="absolute top-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={() => setIsFullscreen(true)}
              className="p-2 bg-black/50 hover:bg-black/70 rounded-lg transition-colors"
              title="View fullscreen"
            >
              <Eye className="w-4 h-4 text-white" />
            </button>
            <button
              onClick={handleDownload}
              className="p-2 bg-black/50 hover:bg-black/70 rounded-lg transition-colors"
              title="Download image"
            >
              <Download className="w-4 h-4 text-white" />
            </button>
            <button
              onClick={handleOpenInNewTab}
              className="p-2 bg-black/50 hover:bg-black/70 rounded-lg transition-colors"
              title="Open in new tab"
            >
              <ExternalLink className="w-4 h-4 text-white" />
            </button>
          </div>
        )}

        {/* Caption */}
        {caption && (
          <div className="p-3 bg-background-tertiary border-t border-border">
            <p className="text-sm text-foreground-secondary">{caption}</p>
          </div>
        )}
      </div>

      {/* Fullscreen modal */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4">
          <div className="relative max-w-full max-h-full">
            <button
              onClick={() => setIsFullscreen(false)}
              className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 rounded-lg transition-colors z-10"
            >
              <X className="w-6 h-6 text-white" />
            </button>
            <img
              src={src}
              alt={alt}
              className="max-w-full max-h-full object-contain"
            />
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              <button
                onClick={handleDownload}
                className="px-4 py-2 bg-kana-accent hover:bg-kana-accent/90 text-white rounded-lg transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </button>
              <button
                onClick={handleOpenInNewTab}
                className="px-4 py-2 bg-background-secondary hover:bg-background-hover text-foreground rounded-lg transition-colors flex items-center space-x-2"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Open in New Tab</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ImageDisplay
