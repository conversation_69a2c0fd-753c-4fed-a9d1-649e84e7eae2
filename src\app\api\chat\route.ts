import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY || '')

// Function definitions for AI tools
const functionDefinitions = [
  {
    name: 'create_image',
    description: 'Generate an image based on a text prompt using A4F API',
    parameters: {
      type: 'object',
      properties: {
        prompt: {
          type: 'string',
          description: 'The text prompt describing the image to generate'
        },
        size: {
          type: 'string',
          description: 'Image size (e.g., "1024x1024")',
          enum: ['512x512', '1024x1024', '1024x768', '768x1024']
        },
        n: {
          type: 'number',
          description: 'Number of images to generate (1-4)',
          minimum: 1,
          maximum: 4
        }
      },
      required: ['prompt']
    }
  },
  {
    name: 'edit_image',
    description: 'Edit an existing image with a text prompt using A4F API',
    parameters: {
      type: 'object',
      properties: {
        prompt: {
          type: 'string',
          description: 'The text prompt describing the edit to make'
        },
        imageUrl: {
          type: 'string',
          description: 'URL of the image to edit'
        }
      },
      required: ['prompt', 'imageUrl']
    }
  },
  {
    name: 'generate_video',
    description: 'Generate a video based on a text prompt using A4F API',
    parameters: {
      type: 'object',
      properties: {
        prompt: {
          type: 'string',
          description: 'The text prompt describing the video to generate'
        },
        ratio: {
          type: 'string',
          description: 'Video aspect ratio',
          enum: ['16:9', '9:16', '1:1', '4:3']
        },
        quality: {
          type: 'string',
          description: 'Video quality',
          enum: ['720p', '1080p', '4K']
        },
        duration: {
          type: 'number',
          description: 'Video duration in seconds (1-10)',
          minimum: 1,
          maximum: 10
        }
      },
      required: ['prompt']
    }
  },
  {
    name: 'web_search',
    description: 'Search the web for current information using Tavily API',
    parameters: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'The search query'
        },
        maxResults: {
          type: 'number',
          description: 'Maximum number of results to return (1-10)',
          minimum: 1,
          maximum: 10
        }
      },
      required: ['query']
    }
  },
  {
    name: 'web_extract',
    description: 'Extract data from specific websites using Tavily API',
    parameters: {
      type: 'object',
      properties: {
        urls: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of URLs to extract data from'
        },
        extractDepth: {
          type: 'string',
          description: 'Depth of extraction',
          enum: ['basic', 'advanced']
        },
        includeImages: {
          type: 'boolean',
          description: 'Whether to include images in extraction'
        }
      },
      required: ['urls']
    }
  },
  {
    name: 'web_crawl',
    description: 'Crawl a website comprehensively using Tavily API',
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The URL to crawl'
        },
        extractDepth: {
          type: 'string',
          description: 'Depth of extraction',
          enum: ['basic', 'advanced']
        },
        categories: {
          type: 'array',
          items: { type: 'string' },
          description: 'Categories to focus on during crawling'
        }
      },
      required: ['url']
    }
  }
]

export async function POST(request: NextRequest) {
  try {
    const { messages, isThinkMode, model = 'gemini-2.5-flash', apiKeys } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      )
    }

    // Use provided API keys or fallback to environment variables
    const geminiApiKey = apiKeys?.gemini || process.env.GOOGLE_GEMINI_API_KEY

    if (!geminiApiKey) {
      return NextResponse.json(
        { error: 'Gemini API key is required' },
        { status: 400 }
      )
    }

    // Initialize Gemini with the provided API key
    const geminiAI = new GoogleGenerativeAI(geminiApiKey)

    // Select model based on think mode
    const selectedModel = isThinkMode ? 'gemini-2.5-pro' : 'gemini-2.5-flash'
    const geminiModel = geminiAI.getGenerativeModel({
      model: selectedModel,
      tools: [{ functionDeclarations: functionDefinitions }]
    })

    // Convert messages to Gemini format
    const geminiMessages = messages.map((msg: any) => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }]
    }))

    // Start chat session
    const chat = geminiModel.startChat({
      history: geminiMessages.slice(0, -1),
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192,
      }
    })

    // Get the latest user message
    const latestMessage = messages[messages.length - 1]
    
    // Send message and get response
    const result = await chat.sendMessage(latestMessage.content)
    const response = await result.response

    // Check if there are function calls
    const functionCalls = response.functionCalls()
    
    if (functionCalls && functionCalls.length > 0) {
      // Handle function calls
      const functionResults = []
      
      for (const call of functionCalls) {
        try {
          const result = await handleFunctionCall(call.name, call.args, apiKeys)
          functionResults.push({
            name: call.name,
            response: result
          })
        } catch (error) {
          console.error(`Error executing function ${call.name}:`, error)
          functionResults.push({
            name: call.name,
            response: { error: 'Function execution failed' }
          })
        }
      }

      // Send function results back to model
      const functionResponse = await chat.sendMessage([
        {
          functionResponse: {
            name: functionCalls[0].name,
            response: functionResults[0].response
          }
        }
      ])

      const finalResponse = await functionResponse.response
      
      return NextResponse.json({
        content: finalResponse.text(),
        functionCalls: functionResults,
        model: selectedModel,
        isThinkMode
      })
    }

    // Regular text response
    return NextResponse.json({
      content: response.text(),
      model: selectedModel,
      isThinkMode
    })

  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    )
  }
}

// Function to handle AI function calls
async function handleFunctionCall(functionName: string, args: any, apiKeys: any = {}) {
  switch (functionName) {
    case 'create_image':
      return await createImage(args, apiKeys)
    case 'edit_image':
      return await editImage(args, apiKeys)
    case 'generate_video':
      return await generateVideo(args, apiKeys)
    case 'web_search':
      return await webSearch(args, apiKeys)
    case 'web_extract':
      return await webExtract(args, apiKeys)
    case 'web_crawl':
      return await webCrawl(args, apiKeys)
    default:
      throw new Error(`Unknown function: ${functionName}`)
  }
}

// A4F API functions
async function createImage(args: any, apiKeys: any = {}) {
  const apiKey = apiKeys.a4f || process.env.A4F_API_KEY

  if (!apiKey) {
    throw new Error('A4F API key is required for image generation')
  }

  const response = await fetch('https://api.a4f.co/v1/images/generations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'provider-4/imagen-4',
      prompt: args.prompt,
      n: args.n || 1,
      size: args.size || '1024x1024'
    })
  })

  if (!response.ok) {
    throw new Error('Failed to generate image')
  }

  return await response.json()
}

async function editImage(args: any, apiKeys: any = {}) {
  const apiKey = apiKeys.a4f || process.env.A4F_API_KEY

  if (!apiKey) {
    throw new Error('A4F API key is required for image editing')
  }

  // Note: This would need to handle file upload differently in a real implementation
  const formData = new FormData()
  formData.append('prompt', args.prompt)
  formData.append('model', 'provider-3/flux-kontext-pro')

  const response = await fetch('https://api.a4f.co/v1/images/edits', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`
    },
    body: formData
  })

  if (!response.ok) {
    throw new Error('Failed to edit image')
  }

  return await response.json()
}

async function generateVideo(args: any, apiKeys: any = {}) {
  const apiKey = apiKeys.a4f || process.env.A4F_API_KEY

  if (!apiKey) {
    throw new Error('A4F API key is required for video generation')
  }

  const response = await fetch('https://api.a4f.co/v1/video/generations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'provider-6/wan-2.1',
      prompt: args.prompt,
      ratio: args.ratio || '16:9',
      quality: args.quality || '720p',
      duration: args.duration || 4
    })
  })

  if (!response.ok) {
    throw new Error('Failed to generate video')
  }

  return await response.json()
}

// Tavily API functions
async function webSearch(args: any, apiKeys: any = {}) {
  const apiKey = apiKeys.tavily || process.env.TAVILY_API_KEY

  if (!apiKey) {
    throw new Error('Tavily API key is required for web search')
  }

  const { tavily } = require('@tavily/core')
  const client = tavily({ apiKey })

  return await client.search(args.query, {
    maxResults: args.maxResults || 5
  })
}

async function webExtract(args: any, apiKeys: any = {}) {
  const apiKey = apiKeys.tavily || process.env.TAVILY_API_KEY

  if (!apiKey) {
    throw new Error('Tavily API key is required for web extraction')
  }

  const { tavily } = require('@tavily/core')
  const client = tavily({ apiKey })

  return await client.extract(args.urls, {
    extractDepth: args.extractDepth || 'advanced',
    format: 'text',
    includeImages: args.includeImages || true,
    includeFavicon: true
  })
}

async function webCrawl(args: any, apiKeys: any = {}) {
  const apiKey = apiKeys.tavily || process.env.TAVILY_API_KEY

  if (!apiKey) {
    throw new Error('Tavily API key is required for web crawling')
  }

  const { tavily } = require('@tavily/core')
  const client = tavily({ apiKey })

  return await client.crawl(args.url, {
    extractDepth: args.extractDepth || 'advanced',
    format: 'text',
    categories: args.categories || [],
    includeImages: true,
    includeFavicon: true
  })
}
