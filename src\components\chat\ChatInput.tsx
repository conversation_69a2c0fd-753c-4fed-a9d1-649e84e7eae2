'use client'

import React, { useState, useRef, useCallback } from 'react'
import { 
  Send, 
  Paperclip, 
  Mic, 
  Image as ImageIcon, 
  Video, 
  Globe, 
  Brain,
  ChevronDown,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ChatInputProps {
  onSendMessage: (content: string, attachments?: File[]) => void
  isLoading: boolean
  isThinkMode: boolean
  onToggleThinkMode: () => void
  currentModel: string
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  isLoading,
  isThinkMode,
  onToggleThinkMode,
  currentModel
}) => {
  const [message, setMessage] = useState('')
  const [attachments, setAttachments] = useState<File[]>([])
  const [showModelSelector, setShowModelSelector] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value)
    adjustTextareaHeight()
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleSend = () => {
    if ((!message.trim() && attachments.length === 0) || isLoading) return
    
    onSendMessage(message, attachments)
    setMessage('')
    setAttachments([])
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setAttachments(prev => [...prev, ...files])
  }

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index))
  }

  const handleAttachClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-3">
      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {attachments.map((file, index) => (
            <div
              key={index}
              className="flex items-center space-x-2 bg-background-tertiary px-3 py-2 rounded-lg"
            >
              <span className="text-sm text-foreground truncate max-w-32">
                {file.name}
              </span>
              <button
                onClick={() => removeAttachment(index)}
                className="text-foreground-secondary hover:text-foreground"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Main Input Area */}
      <div className="relative">
        <div className="flex items-end space-x-3 bg-chat-input-bg border border-chat-input-border rounded-2xl p-3 focus-within:border-chat-input-focus transition-colors">
          {/* Attachment Button */}
          <button
            onClick={handleAttachClick}
            className="flex-shrink-0 p-2 hover:bg-background-hover rounded-lg transition-colors"
            disabled={isLoading}
          >
            <Paperclip className="w-5 h-5 text-foreground-secondary" />
          </button>

          {/* Text Input */}
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="What do you want to know?"
              className="w-full bg-transparent text-foreground placeholder-foreground-muted resize-none outline-none min-h-[24px] max-h-[120px]"
              rows={1}
              disabled={isLoading}
            />
          </div>

          {/* Send Button */}
          <button
            onClick={handleSend}
            disabled={(!message.trim() && attachments.length === 0) || isLoading}
            className={cn(
              "flex-shrink-0 p-2 rounded-lg transition-colors",
              (!message.trim() && attachments.length === 0) || isLoading
                ? "text-foreground-muted cursor-not-allowed"
                : "text-foreground hover:bg-background-hover"
            )}
          >
            <Send className="w-5 h-5" />
          </button>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,video/*,.pdf,.doc,.docx,.txt"
        />
      </div>

      {/* Bottom Controls */}
      <div className="flex items-center justify-between">
        {/* Feature Buttons */}
        <div className="flex items-center space-x-2">
          <button className="flex items-center space-x-2 px-3 py-2 bg-background-secondary hover:bg-background-hover rounded-lg transition-colors">
            <ImageIcon className="w-4 h-4 text-foreground-secondary" />
            <span className="text-sm text-foreground-secondary">Create Image</span>
          </button>
          
          <button className="flex items-center space-x-2 px-3 py-2 bg-background-secondary hover:bg-background-hover rounded-lg transition-colors">
            <Video className="w-4 h-4 text-foreground-secondary" />
            <span className="text-sm text-foreground-secondary">Edit Image</span>
          </button>
          
          <button className="flex items-center space-x-2 px-3 py-2 bg-background-secondary hover:bg-background-hover rounded-lg transition-colors">
            <Globe className="w-4 h-4 text-foreground-secondary" />
            <span className="text-sm text-foreground-secondary">Latest News</span>
          </button>
          
          <button className="flex items-center space-x-2 px-3 py-2 bg-background-secondary hover:bg-background-hover rounded-lg transition-colors">
            <Mic className="w-4 h-4 text-foreground-secondary" />
            <span className="text-sm text-foreground-secondary">Personas</span>
          </button>
        </div>

        {/* Model Selector and Think Mode */}
        <div className="flex items-center space-x-3">
          {/* Think Mode Toggle */}
          <button
            onClick={onToggleThinkMode}
            className={cn(
              "flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors",
              isThinkMode
                ? "bg-kana-accent text-white"
                : "bg-background-secondary hover:bg-background-hover text-foreground-secondary"
            )}
          >
            <Brain className="w-4 h-4" />
            <span className="text-sm">Think</span>
          </button>

          {/* Model Selector */}
          <div className="relative">
            <button
              onClick={() => setShowModelSelector(!showModelSelector)}
              className="flex items-center space-x-2 px-3 py-2 bg-background-secondary hover:bg-background-hover rounded-lg transition-colors"
            >
              <span className="text-sm text-foreground">{currentModel}</span>
              <ChevronDown className="w-4 h-4 text-foreground-secondary" />
            </button>

            {showModelSelector && (
              <div className="absolute bottom-full right-0 mb-2 bg-background-secondary border border-border rounded-lg shadow-lg py-2 min-w-32">
                <button className="w-full text-left px-3 py-2 text-sm text-foreground hover:bg-background-hover">
                  Kana 3
                </button>
                <button className="w-full text-left px-3 py-2 text-sm text-foreground hover:bg-background-hover">
                  Kana 3 Pro
                </button>
              </div>
            )}
          </div>

          {/* Voice Input */}
          <button className="p-2 hover:bg-background-hover rounded-lg transition-colors">
            <Mic className="w-5 h-5 text-foreground-secondary" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default ChatInput
